"""
Question answering routes for the DSPy Multi-Agent System API.

Handles simple and advanced question processing endpoints.
"""

import logging
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from typing import Optional
from fastapi.responses import StreamingResponse
import dspy
from src.agents.specialists.react_search_specialist import <PERSON><PERSON><PERSON><PERSON><PERSON>chSpecialist
from src.api.utils.session_storage import get_session_storage
import tiktoken
import os
import asyncio

from ..models.requests import SimpleQuestionRequest, AdvancedQuestionRequest
from ..models.responses import QuestionResponse
from ..models.errors import ValidationError, InternalServerError
from ..middleware.auth import get_api_key
from ..middleware.session import get_request_id
from ..services.workflow_service import get_workflow_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/questions/simple")
async def simple_question_with_sse(
    request: SimpleQuestionRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(get_api_key),
):
    """
    Simple question endpoint with real-time SSE updates.
    
    **New SSE-based Architecture:**
    - Returns immediate response with session_id
    - Processes question asynchronously in background
    - Real-time tool updates via SSE endpoint: `/api/v1/sse/sessions/{session_id}`
    - Final answer delivered via SSE when complete
    
    **Usage:**
    1. POST to this endpoint to start processing
    2. Connect to SSE endpoint using returned session_id
    3. Monitor real-time tool execution updates
    4. Receive final answer via SSE
    """
    try:
        if not request.session_id or len(request.session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty", field="session_id")

        logger.info(f"Starting question processing for session {request.session_id}")
        
        # Add background task for question processing
        background_tasks.add_task(
            process_question_async,
            request.session_id,
            request.question
        )
        
        # Return immediate response
        return {
            "success": True,
            "session_id": request.session_id,
            "message": "Question processing started",
            "sse_endpoint": f"/api/v1/sse/sessions/{request.session_id}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except ValidationError:
        # Re-raise validation errors as-is
        raise
    except Exception as e:
        logger.error(f"Failed to start question processing: {e}")
        raise InternalServerError(f"Failed to start question processing: {str(e)}")


async def process_question_async(session_id: str, question: str):
    """
    Background task for processing questions with real-time SSE updates.
    
    This function runs independently of the HTTP response, allowing
    true real-time updates via SSE without blocking the response.
    """
    try:
        logger.info(f"Background processing started for session {session_id}")
        
        # Get SSE manager for real-time updates
        from ..main import sse_manager
        
        # --- Session Storage ---
        session_storage = get_session_storage()
        session_data = await session_storage.get_session(session_id) or {}
        chat_history = session_data.get("chat_history", [])
        
        # Debug logging
        print(f"🔍 Session {session_id}: Retrieved {len(chat_history)} messages from history")
        logger.info(f"Session {session_id}: Retrieved {len(chat_history)} messages from history")

        # --- Token Counting ---
        model_name = os.getenv("OPENAI_MODEL", "gpt-4.1-mini")
        try:
            # Map new model names to known tokenizers
            if "gpt-4.1" in model_name or "gpt-4o" in model_name:
                enc = tiktoken.get_encoding("cl100k_base")  # GPT-4 tokenizer
            else:
                enc = tiktoken.encoding_for_model(model_name)
        except KeyError:
            # Fallback to a default tokenizer if model not found
            enc = tiktoken.get_encoding("cl100k_base")
        
        def count_tokens(text):
            return len(enc.encode(text))

        # --- Append new user message ---
        chat_history.append({"role": "user", "content": question})
        
        # --- Save user message to session immediately ---
        session_data["chat_history"] = chat_history
        await session_storage.update_session(session_id, session_data)
        
        # --- Flatten history for context ---
        context_text = "\n".join([f"{m['role']}: {m['content']}" for m in chat_history])
        total_tokens = count_tokens(context_text)

        # --- Context Window Management ---
        max_context = 128000 if "gpt-4.1" in model_name else 4096
        if total_tokens > 0.5 * max_context:
            summarizer = dspy.Predict("history -> summary")
            summary = summarizer(history=context_text).summary
            chat_history = [{"role": "system", "content": f"Summary of previous conversation: {summary}"}]
            context_text = chat_history[0]["content"]
            total_tokens = count_tokens(context_text)
            
            # --- Save summarized history ---
            session_data["chat_history"] = chat_history
            await session_storage.update_session(session_id, session_data)

        # --- DSPy ReAct Agent with Context and SSE Updates ---
        # Use SSE manager for real-time tool updates
        agent = ReActSearchSpecialist(sse_manager=sse_manager, session_id=session_id)
        
        # Get current date and time for context
        current_time = datetime.now(timezone.utc)
        current_date_time = current_time.strftime("%A, %B %d, %Y at %H:%M:%S UTC")
        
        # Add current date/time context to the question
        time_context = f"Current date and time: {current_date_time}. "
        
        try:
            # Create a context-aware question that includes chat history
            print(f"🔍 Session {session_id}: Chat history length: {len(chat_history)}")
            print(f"🔍 Session {session_id}: Context text: {context_text[:200]}...")
            
            if len(chat_history) > 1:  # More than just the current question
                context_aware_question = f"""Previous conversation context:
{context_text}

Current question: {time_context}{question}

Please answer the current question, taking into account the previous conversation context where relevant."""
                print(f"🔍 Session {session_id}: Using context-aware question")
            else:
                context_aware_question = time_context + question
                print(f"🔍 Session {session_id}: Using simple question (no context)")
            
            # Use async ReAct agent - this will send real-time tool updates via SSE
            result = await agent.aforward(question=context_aware_question, context="")
            response_text = result.answer
            
            # Add assistant response to chat history IMMEDIATELY
            chat_history.append({"role": "assistant", "content": response_text})
            session_data["chat_history"] = chat_history
            
            print(f"🔍 Saving session with {len(chat_history)} messages")
            update_success = await session_storage.update_session(session_id, session_data)
            if not update_success:
                # Session doesn't exist, create it
                print(f"🔍 Update failed, creating new session...")
                await session_storage.create_session(session_id, session_data)
            print(f"🔍 Session update success: {update_success}")
            
            # Send final answer via SSE
            await sse_manager.send_question_complete(session_id, response_text)
            
            logger.info(f"Question processing completed for session {session_id}")
            
        except Exception as agent_error:
            error_msg = f"Agent execution failed: {str(agent_error)}"
            logger.error(f"Agent error for session {session_id}: {error_msg}")
            await sse_manager.send_error_notification(session_id, error_msg)
            
    except Exception as e:
        error_msg = f"Background processing failed: {str(e)}"
        logger.error(f"Background processing error for session {session_id}: {error_msg}")
        try:
            await sse_manager.send_error_notification(session_id, error_msg)
        except:
            pass  # Don't let SSE errors prevent logging


@router.post("/questions/advanced", response_model=QuestionResponse)  
async def advanced_question(
    request: AdvancedQuestionRequest,
    background_tasks: BackgroundTasks,
    api_key: str = Depends(get_api_key),
    workflow_service = Depends(get_workflow_service)
):
    """
    Process complex question with advanced configuration.
    
    This endpoint:
    - Supports both standard and enhanced workflows
    - Handles questions up to 2000 characters
    - Accepts advanced configuration options
    - Provides fine-grained control over execution
    
    **Integration:** Calls existing `run_workflow()` function
    """
    try:
        logger.info(
            f"Advanced question request: session={request.session_id}, "
            f"type={request.workflow_type}, question_length={len(request.question)}"
        )
        
        # Validate session ID
        if not request.session_id or len(request.session_id.strip()) == 0:
            raise ValidationError("Session ID cannot be empty", field="session_id")
        
        # Prepare advanced configuration
        execution_config = dict(request.config or {})
        execution_config.update({
            "workflow_source": "api_advanced",
            "api_request": True
        })
        
        # Determine workflow type
        workflow_type = "enhanced" if request.workflow_type == "enhanced" else "standard"
        
        logger.debug(f"Using workflow type: {workflow_type}")
        
        # Start workflow with advanced configuration
        workflow_id = await workflow_service.start_workflow(
            question=request.question,
            session_id=request.session_id,
            workflow_type=workflow_type,
            config=execution_config
        )
        
        logger.info(f"Advanced workflow started: {workflow_id} (type: {workflow_type})")
        
        return QuestionResponse(
            success=True,
            workflow_id=workflow_id,
            timestamp=datetime.now(timezone.utc)
        )
        
    except ValidationError:
        # Re-raise validation errors as-is
        raise
    except Exception as e:
        logger.error(f"Advanced question processing failed: {e}")
        raise InternalServerError(f"Failed to process advanced question: {str(e)}")


@router.get("/questions/health")
async def questions_health():
    """Health check for questions endpoints."""
    try:
        # Light health check - don't initialize heavy services
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "endpoints": {
                "simple": "available",
                "advanced": "available"
            },
            "workflow_service": "ready"
        }
        
    except Exception as e:
        logger.error(f"Questions health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Questions service unhealthy: {str(e)}"
        )


# Additional utility endpoints

@router.get("/questions/limits")
async def get_question_limits():
    """Get question processing limits and constraints."""
    return {
        "simple_questions": {
            "min_length": 10,
            "max_length": 1000,
            "supported_languages": ["en", "es", "fr", "de", "it", "pt"],
            "workflow_type": "standard"
        },
        "advanced_questions": {
            "min_length": 10,
            "max_length": 2000,
            "workflow_types": ["standard", "enhanced"],
            "config_options": {
                "enable_optimization": "boolean",
                "max_iterations": "integer (1-10)",
                "vector_knowledge_enabled": "boolean",
                "parallel_execution": "boolean",
                "quality_threshold": "float (0.0-1.0)",
                "specialist_config": "object"
            }
        },
        "rate_limits": {
            "requests_per_minute": 60,
            "concurrent_workflows": 5
        }
    }


@router.get("/questions/examples")
async def get_question_examples():
    """Get example questions for testing."""
    return {
        "simple_examples": [
            "What are the latest developments in renewable energy?",
            "How does artificial intelligence impact healthcare?",
            "What are the benefits of electric vehicles?",
            "Explain quantum computing in simple terms.",
            "What is the future of remote work?"
        ],
        "advanced_examples": [
            {
                "question": "Analyze the economic impact of artificial intelligence on employment in the next decade",
                "workflow_type": "enhanced",
                "config": {
                    "enable_optimization": True,
                    "max_iterations": 5,
                    "vector_knowledge_enabled": True,
                    "parallel_execution": True,
                    "quality_threshold": 0.8
                }
            },
            {
                "question": "Compare the environmental benefits and challenges of solar vs wind energy",
                "workflow_type": "standard",
                "config": {
                    "specialist_config": {
                        "include_predictions": True,
                        "focus_areas": ["environmental", "economic", "technical"]
                    }
                }
            }
        ]
    }
