"""
Request models for the DSPy Multi-Agent System API.

Defines Pydantic models for all API request payloads with validation.
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any
from enum import Enum


class WorkflowType(str, Enum):
    """Workflow execution types."""
    standard = "standard"
    enhanced = "enhanced"


class SimpleQuestionRequest(BaseModel):
    """Request model for simple question answering."""
    question: str = Field(
        ..., 
        min_length=10, 
        max_length=1000,
        description="The question to answer (10-1000 characters)"
    )
    session_id: str = Field(
        ..., 
        min_length=1,
        description="Unique session identifier"
    )
    language: Optional[str] = Field(
        None,
        pattern=r'^[a-z]{2}(-[A-Z]{2})?$',
        description="Response language (ISO 639-1 code, e.g., 'en', 'en-US')"
    )

    @field_validator('question')
    @classmethod
    def validate_question(cls, v):
        """Validate question content."""
        if not v.strip():
            raise ValueError('Question cannot be empty or whitespace only')
        return v.strip()

    @field_validator('session_id')
    @classmethod
    def validate_session_id(cls, v):
        """Validate session ID format."""
        if not v.strip():
            raise ValueError('Session ID cannot be empty')
        return v.strip()


class AdvancedQuestionRequest(BaseModel):
    """Request model for advanced question answering with configuration."""
    question: str = Field(
        ..., 
        min_length=10, 
        max_length=2000,
        description="Complex question to answer (10-2000 characters)"
    )
    session_id: str = Field(
        ..., 
        min_length=1,
        description="Unique session identifier"
    )
    workflow_type: Optional[WorkflowType] = Field(
        WorkflowType.standard,
        description="Workflow execution type"
    )
    config: Optional[Dict[str, Any]] = Field(
        None,
        description="Advanced configuration options"
    )

    @field_validator('question')
    @classmethod
    def validate_question(cls, v):
        """Validate question content."""
        if not v.strip():
            raise ValueError('Question cannot be empty or whitespace only')
        return v.strip()

    @field_validator('config')
    @classmethod
    def validate_config(cls, v):
        """Validate configuration structure."""
        if v is not None:
            # Validate known configuration keys
            allowed_keys = {
                'enable_optimization', 'max_iterations', 'vector_knowledge_enabled',
                'parallel_execution', 'quality_threshold', 'specialist_config',
                'depth', 'include_examples', 'response_format', 'detail_level',
                'include_sources', 'max_response_length', 'language', 'style'
            }
            if not isinstance(v, dict):
                raise ValueError('Config must be a dictionary')

            # Check for unknown keys (warning, not error)
            unknown_keys = set(v.keys()) - allowed_keys
            if unknown_keys:
                print(f"Warning: Unknown config keys: {unknown_keys}")

        return v


class FileUploadMetadata(BaseModel):
    """Metadata for file upload requests."""
    file_name: Optional[str] = Field(
        None,
        description="Optional custom filename"
    )
    session_id: str = Field(
        ..., 
        min_length=1,
        description="Session ID for file isolation"
    )

    @field_validator('file_name')
    @classmethod
    def validate_file_name(cls, v):
        """Validate custom filename."""
        if v is not None:
            v = v.strip()
            if not v:
                return None
            # Basic filename validation
            invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
            if any(char in v for char in invalid_chars):
                raise ValueError(f'Filename contains invalid characters: {invalid_chars}')
        return v

    @field_validator('session_id')
    @classmethod
    def validate_session_id(cls, v):
        """Validate session ID."""
        if not v.strip():
            raise ValueError('Session ID cannot be empty')
        return v.strip()


class WorkflowCancelRequest(BaseModel):
    """Request model for workflow cancellation."""
    reason: Optional[str] = Field(
        None,
        max_length=500,
        description="Optional reason for cancellation"
    )
