# Core AI/ML Framework Dependencies
dspy>=2.6.0
crewai>=0.120.1
crewai-tools>=0.45.0
langchain>=0.3.25
langchain-cohere>=0.3.5
langchain-community>=0.3.24
langchain-core>=0.3.60
langchain-experimental>=0.3.4
langchain-openai>=0.2.14
langchain-text-splitters>=0.3.8
langsmith>=0.3.42
openai>=1.75.0

# Vector Database Dependencies
faiss-cpu>=1.11.0
chromadb>=0.5.23
qdrant-client>=1.14.2
# pymilvus>=2.4.0 

# Embedding Service Dependencies
sentence-transformers>=4.1.0
setuptools>=80.8.0
tiktoken>=0.9.0
cohere>=5.15.0

# Search and Knowledge Dependencies
duckduckgo_search>=8.0.2
wikipedia>=1.4.0

# Web scraping and analysis dependencies
requests>=2.32.3
beautifulsoup4>=4.13.4
chardet>=5.2.0
lxml>=5.4.0

# Data Processing and Analytics
pandas>=2.2.3
numpy>=2.2.6
matplotlib>=3.10.3
seaborn>=0.13.2

# System Monitoring Dependencies
psutil>=7.0.0

# Multimodal Processing Dependencies
pillow>=11.2.1
pypdf>=5.5.0
pypdfium2>=4.30.1

# Configuration and Utilities
#pyyaml>=6.0
python-dateutil>=2.9.0
pydantic>=2.11.4
pydantic-settings>=2.9.1
pydantic_core>=2.33.2

# Database Dependencies
aiosqlite>=0.20.0

# Development and Testing Dependencies
pytest>=8.3.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0

# Optional Performance Dependencies
# uvloop>=0.19.0  # For async performance on Linux/macOS
# orjson>=3.9.0   # Fast JSON processing

# uvicorn>=0.30.0
fastapi>=0.115.9
uvicorn>=0.34.2
websocket-client>=1.8.0
websockets>=15.0.1
aiofiles>=24.1.0
aiohttp>=3.11.18
python-multipart>=0.0.20
redis>=6.1.0
aioredis>=2.0.1
httpx>=0.28.1
pytest-asyncio>=0.26.0
# gunicorn>=22.0.0 